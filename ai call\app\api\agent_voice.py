import os, json, base64, aiohttp, sys
import logging
from fastapi import APIRouter, WebSocket
from dotenv import load_dotenv
from app.services.whisper_service import transcribe_audio
from app.services.llm_service import generate_response

# Set up logging with immediate flushing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Force stdout to be unbuffered for immediate output
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

load_dotenv()
API_KEY  = os.getenv("ELEVEN_LABS_API_KEY")         # ← make sure this exists
VOICE_ID = "JBFqnCBsd6RMkjVDRZzb"
MODEL_ID = "eleven_turbo_v2_5"

router = APIRouter()

@router.websocket("/agent/voice")
async def agent_voice(ws: WebSocket):
    logger.info("🔗 WebSocket connection initiated")
    sys.stdout.flush()
    await ws.accept()
    logger.info("✅ WebSocket connection accepted - ready for continuous conversation")
    sys.stdout.flush()

    conversation_turn = 0

    try:
        # Keep the connection open for continuous conversation
        while True:
            conversation_turn += 1
            logger.info(f"🔄 Starting conversation turn #{conversation_turn}")
            sys.stdout.flush()

            # 1️⃣ receive caller audio
            logger.info("📥 Waiting for audio data from client...")
            sys.stdout.flush()

            try:
                message = await ws.receive()
                if message["type"] != "websocket.receive" or "bytes" not in message:
                    logger.error("❌ Invalid message type or no bytes received")
                    sys.stdout.flush()
                    break
                audio_bytes = message["bytes"]
                logger.info(f"🎵 Received audio data: {len(audio_bytes)} bytes")
                sys.stdout.flush()
            except Exception as e:
                logger.info(f"📞 Client disconnected or connection closed: {e}")
                break

            # 2️⃣ STT ➜ LLM
            logger.info("🎤 Starting speech-to-text transcription...")
            user_text = transcribe_audio(audio_bytes)
            if not user_text:
                logger.error("❌ Transcription failed - no text returned, continuing to wait for next audio...")
                await ws.send_json({"error": "could_not_transcribe"})
                continue  # Don't close connection, just continue to next turn
            logger.info(f"📝 Transcription successful: '{user_text}'")

            logger.info("🧠 Generating AI response...")
            sys.stdout.flush()
            agent_reply = generate_response(user_text)
            logger.info(f"💬 AI response generated: '{agent_reply}'")
            sys.stdout.flush()

            logger.info("📤 Sending transcription and AI response to client...")
            sys.stdout.flush()
            await ws.send_json({"user_text": user_text, "agent_reply": agent_reply})
            logger.info("✅ Text response sent successfully")
            sys.stdout.flush()

            # 3️⃣ ElevenLabs streaming (aiohttp WebSocket)
            logger.info("🔊 Starting ElevenLabs TTS streaming...")
            url = (
                f"wss://api.elevenlabs.io/v1/text-to-speech/"
                f"{VOICE_ID}/stream-input?model_id={MODEL_ID}"
            )
            logger.info(f"🌐 Connecting to ElevenLabs: {url}")

            async with aiohttp.ClientSession() as session:
                async with session.ws_connect(url, max_msg_size=0) as el_ws:
                    logger.info("✅ Connected to ElevenLabs WebSocket")

                    # initialise connection (key inside JSON)
                    logger.info("🔑 Initializing ElevenLabs connection...")
                    await el_ws.send_json({
                        "text": " ",
                        "xi_api_key": API_KEY,
                        "voice_settings": {
                            "stability": 0.3,
                            "similarity_boost": 0.8,
                            "use_speaker_boost": False
                        },
                        "generation_config": {"chunk_length_schedule": [50, 100]}
                    })
                    logger.info("✅ ElevenLabs connection initialized")

                    # send LLM reply
                    logger.info(f"📤 Sending text to ElevenLabs: '{agent_reply}'")
                    await el_ws.send_json({"text": agent_reply, "flush": True})
                    await el_ws.send_json({"text": ""})          # end marker
                    logger.info("✅ Text sent to ElevenLabs, waiting for audio chunks...")

                    # relay audio chunks to the caller
                    audio_chunks_received = 0
                    async for msg in el_ws:
                        if msg.type is aiohttp.WSMsgType.TEXT:
                            data = json.loads(msg.data)
                            audio_b64 = data.get("audio")
                            if audio_b64:                        # skip control packets
                                audio_chunks_received += 1
                                logger.info(f"🎵 Received audio chunk #{audio_chunks_received}, sending to client...")
                                await ws.send_bytes(base64.b64decode(audio_b64))
                            if data.get("isFinal"):
                                logger.info("🏁 ElevenLabs marked stream as final")
                                break
                        elif msg.type is aiohttp.WSMsgType.ERROR:
                            logger.error("❌ Error in ElevenLabs WebSocket stream")
                            break

                    logger.info(f"✅ TTS streaming complete. Total audio chunks: {audio_chunks_received}")

            logger.info(f"🔄 Conversation turn #{conversation_turn} completed, ready for next turn...")
            sys.stdout.flush()

    except Exception as e:
        logger.error(f"❌ Error in WebSocket conversation loop: {e}")
    finally:
        logger.info(f"🔚 WebSocket connection ended after {conversation_turn} conversation turns")
        # Don't explicitly close the connection here - let it stay open for continuous conversation
        # The connection will be closed naturally when the client disconnects
