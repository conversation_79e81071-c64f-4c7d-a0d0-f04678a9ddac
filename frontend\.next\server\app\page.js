/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2hhY2tvbl90dHMlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3RzXFxcXGhhY2tvbl90dHNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\projects\\hackon_tts\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2hhY2tvbl90dHMlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3RzXFxcXGhhY2tvbl90dHNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Ready\");\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const webSocketRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleToggleRecording = async ()=>{\n        if (isRecording) {\n            // Stop recording\n            console.log(\"🛑 Stopping recording...\");\n            setStatus(\"Stopping...\");\n            // Stop MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                console.log(`📹 MediaRecorder state: ${mediaRecorderRef.current.state}`);\n                mediaRecorderRef.current.stop();\n            }\n            // Stop media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n            // Close WebSocket connection\n            if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {\n                console.log(\"🔌 Closing WebSocket connection...\");\n                webSocketRef.current.close();\n                webSocketRef.current = null;\n            }\n            setIsRecording(false);\n            setStatus(\"Ready\");\n        } else {\n            // Start recording\n            console.log(\"🎙️ Starting recording...\");\n            setStatus(\"Starting...\");\n            await startRecording();\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            console.log(\"🎤 Getting microphone access...\");\n            setStatus(\"Getting microphone access...\");\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            console.log(\"✅ Microphone access granted\");\n            console.log(`🎵 Audio stream settings: ${stream.getAudioTracks()[0].getSettings().sampleRate}Hz, ${stream.getAudioTracks()[0].getSettings().channelCount} channel(s)`);\n            streamRef.current = stream;\n            // Establish WebSocket connection first\n            setStatus(\"Connecting to server...\");\n            console.log(`🔗 Establishing WebSocket connection to: ws://127.0.0.1:8000/api/agent/voice`);\n            const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n            webSocketRef.current = socket;\n            socket.onopen = ()=>{\n                console.log(\"✅ WebSocket connected! Starting audio streaming...\");\n                setStatus(\"Connected - Starting recording...\");\n                // Set up MediaRecorder with timeslice for real-time chunks\n                const mediaRecorder = new MediaRecorder(stream, {\n                    mimeType: \"audio/webm;codecs=opus\"\n                });\n                console.log(`📹 MediaRecorder created with mimeType: audio/webm;codecs=opus`);\n                mediaRecorderRef.current = mediaRecorder;\n                mediaRecorder.ondataavailable = async (event)=>{\n                    if (event.data.size > 0 && socket.readyState === WebSocket.OPEN) {\n                        console.log(`📦 Audio chunk received: ${event.data.size} bytes - streaming to server`);\n                        try {\n                            // Convert chunk to WAV and send immediately\n                            const wavBuffer = await convertToWav(event.data);\n                            console.log(`📤 Sending audio chunk: ${wavBuffer.byteLength} bytes WAV`);\n                            socket.send(wavBuffer);\n                        } catch (error) {\n                            console.error(\"❌ Error converting/sending audio chunk:\", error);\n                        }\n                    }\n                };\n                mediaRecorder.onstop = ()=>{\n                    console.log(\"🛑 MediaRecorder stopped\");\n                    setStatus(\"Recording stopped\");\n                };\n                // Start recording with 1-second timeslices for real-time streaming\n                console.log(\"🔴 Starting MediaRecorder with 1s timeslices...\");\n                mediaRecorder.start(1000); // 1000ms = 1 second chunks\n                setIsRecording(true);\n                setStatus(\"Recording and streaming... Click stop when done\");\n                console.log(\"✅ Recording and streaming started successfully\");\n            };\n            socket.onmessage = async (event)=>{\n                if (typeof event.data === \"string\") {\n                    // Handle JSON response with transcription and AI reply\n                    console.log(`📥 Received text message: ${event.data.length} characters`);\n                    try {\n                        const response = JSON.parse(event.data);\n                        console.log(`📝 Parsed JSON response:`, response);\n                        if (response.user_text && response.agent_reply) {\n                            console.log(`💬 User: \"${response.user_text}\"`);\n                            console.log(`🤖 Agent: \"${response.agent_reply}\"`);\n                            setConversation((prev)=>[\n                                    ...prev,\n                                    {\n                                        userText: response.user_text,\n                                        agentReply: response.agent_reply\n                                    }\n                                ]);\n                            setStatus(\"Received response\");\n                        } else if (response.error) {\n                            console.error(`❌ Server error: ${response.error}`);\n                            setStatus(`Error: ${response.error}`);\n                        }\n                    } catch (error) {\n                        console.error(\"❌ Error parsing JSON:\", error);\n                        setStatus(\"Error parsing response\");\n                    }\n                } else if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {\n                    // Handle MP3 audio response\n                    console.log(`🎵 Received audio response:`, event.data);\n                    let audioSize;\n                    if (event.data instanceof Blob) {\n                        audioSize = event.data.size;\n                    } else {\n                        audioSize = event.data.byteLength;\n                    }\n                    console.log(`🔊 Audio data size: ${audioSize} bytes`);\n                    setStatus(\"Playing AI response...\");\n                    const audioData = event.data instanceof Blob ? event.data : new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    console.log(`🔍 Audio data type: ${audioData.type}, size: ${audioData.size} bytes`);\n                    const audioUrl = URL.createObjectURL(audioData);\n                    console.log(`🎧 Created audio URL for playback: ${audioUrl}`);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onended = ()=>{\n                            console.log(`✅ Audio playback completed`);\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                            setStatus(\"Ready\");\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            console.log(`▶️ Audio playback started`);\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            console.log(`⏸️ Audio playback paused`);\n                            setIsPlayingAudio(false);\n                        };\n                        try {\n                            console.log(`🎧 Starting audio playback...`);\n                            await audioPlayerRef.current.play();\n                        } catch (error) {\n                            console.error(\"❌ Error playing audio:\", error);\n                            setStatus(\"Error playing audio\");\n                        }\n                    }\n                }\n            };\n            socket.onerror = (error)=>{\n                console.error(\"❌ WebSocket error:\", error);\n                setStatus(\"Connection error\");\n            };\n            socket.onclose = (event)=>{\n                console.log(`🔌 WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`);\n                setStatus(\"Connection closed\");\n            };\n        } catch (error) {\n            console.error(\"❌ Error starting recording:\", error);\n            setStatus(\"Error: Could not access microphone\");\n        }\n    };\n    // Helper function to convert audio to WAV format\n    const convertToWav = async (audioBlob)=>{\n        const arrayBuffer = await audioBlob.arrayBuffer();\n        const audioContext = new AudioContext({\n            sampleRate: 16000\n        });\n        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n        // Get mono channel data\n        const channelData = audioBuffer.getChannelData(0);\n        // Convert to 16-bit PCM\n        const pcmData = new Int16Array(channelData.length);\n        for(let i = 0; i < channelData.length; i++){\n            const sample = Math.max(-1, Math.min(1, channelData[i]));\n            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;\n        }\n        // Create WAV buffer\n        const wavBuffer = createWavBuffer(pcmData, 16000);\n        await audioContext.close();\n        return wavBuffer;\n    };\n    // Helper function to create WAV buffer\n    const createWavBuffer = (pcmData, sampleRate)=>{\n        const dataLength = pcmData.length * 2;\n        const buffer = new ArrayBuffer(44 + dataLength);\n        const view = new DataView(buffer);\n        // WAV header\n        view.setUint32(0, 0x52494646, false); // \"RIFF\"\n        view.setUint32(4, 36 + dataLength, true); // File size - 8\n        view.setUint32(8, 0x57415645, false); // \"WAVE\"\n        view.setUint32(12, 0x666d7420, false); // \"fmt \"\n        view.setUint32(16, 16, true); // Subchunk1Size\n        view.setUint16(20, 1, true); // AudioFormat (PCM)\n        view.setUint16(22, 1, true); // NumChannels (mono)\n        view.setUint32(24, sampleRate, true); // SampleRate\n        view.setUint32(28, sampleRate * 2, true); // ByteRate\n        view.setUint16(32, 2, true); // BlockAlign\n        view.setUint16(34, 16, true); // BitsPerSample\n        view.setUint32(36, 0x64617461, false); // \"data\"\n        view.setUint32(40, dataLength, true); // Subchunk2Size\n        // Write PCM data\n        let offset = 44;\n        for(let i = 0; i < pcmData.length; i++){\n            view.setInt16(offset, pcmData[i], true);\n            offset += 2;\n        }\n        return buffer;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-center mb-6\",\n                    children: \"Voice Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggleRecording,\n                                className: `px-6 py-3 rounded-lg text-white font-semibold ${isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"}`,\n                                disabled: status.includes(\"Error\"),\n                                children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mb-4\",\n                            children: [\n                                \"Status: \",\n                                status\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this),\n                        isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Playing AI Response\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Conversation\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                conversation.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b pb-4 last:border-b-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-blue-600\",\n                                                        children: \"You:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-800\",\n                                                        children: entry.userText\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: \"AI:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-800\",\n                                                        children: entry.agentReply\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)),\n                                conversation.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center\",\n                                    children: \"No conversation yet. Start recording to begin!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"Audio Player\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                            ref: audioPlayerRef,\n                            className: \"w-full\",\n                            controls: true,\n                            controlsList: \"nodownload\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();